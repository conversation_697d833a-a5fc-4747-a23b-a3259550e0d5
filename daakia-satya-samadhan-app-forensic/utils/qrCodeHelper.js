export const QR_CODE_TYPES = {
  FORENSIC_REQUEST: 'forensic_request',
  EVIDENCE_PACKAGING: 'evidence_packaging',
  CASE: 'case',
  EVIDENCE: 'evidence',
  UNKNOWN: 'unknown'
};

/**
 * Parse QR code data and determine its type and extracted information
 * @param {string} qrData - The scanned QR code data
 * @returns {object} - Parsed QR code information
 */
export const parseQRCode = (qrData) => {
  try {
    // Handle URL-based QR codes
    if (qrData.startsWith('http://') || qrData.startsWith('https://')) {
      return parseUrlQRCode(qrData);
    }
    
    // Handle direct ID QR codes (if any)
    if (qrData.length === 24 && /^[a-f\d]{24}$/i.test(qrData)) {
      return {
        type: QR_CODE_TYPES.UNKNOWN,
        id: qrData,
        isValid: true,
        data: { id: qrData }
      };
    }
    
    // Handle other formats if needed
    return {
      type: QR_CODE_TYPES.UNKNOWN,
      isValid: false,
      error: 'Unrecognized QR code format',
      rawData: qrData
    };
    
  } catch (error) {
    return {
      type: QR_CODE_TYPES.UNKNOWN,
      isValid: false,
      error: error.message,
      rawData: qrData
    };
  }
};

/**
 * Parse URL-based QR codes
 * @param {string} urlData - The URL from QR code
 * @returns {object} - Parsed URL information
 */
const parseUrlQRCode = (urlData) => {
  try {
    const url = new URL(urlData);
    const path = url.pathname;
    const pathSegments = path.split('/').filter(segment => segment.length > 0);
    
    // Handle different URL patterns
    if (path.includes('/forensicRequests/')) {
      return parseForensicRequestUrl(pathSegments, url);
    }
    
    if (path.includes('/evidencePackaging/')) {
      return parseEvidencePackagingUrl(pathSegments, url);
    }
    
    if (path.includes('/cases/')) {
      return parseCaseUrl(pathSegments, url);
    }
    
    if (path.includes('/evidences/')) {
      return parseEvidenceUrl(pathSegments, url);
    }
    
    return {
      type: QR_CODE_TYPES.UNKNOWN,
      isValid: false,
      error: 'Unknown URL pattern',
      rawData: urlData,
      path: path
    };
    
  } catch (error) {
    return {
      type: QR_CODE_TYPES.UNKNOWN,
      isValid: false,
      error: 'Invalid URL format',
      rawData: urlData
    };
  }
};

/**
 * Parse forensic request URLs
 * Pattern: /forensicRequests/{forensicRequestId}
 */
const parseForensicRequestUrl = (pathSegments, url) => {
  const forensicRequestIndex = pathSegments.indexOf('forensicRequests');
  
  if (forensicRequestIndex !== -1 && pathSegments[forensicRequestIndex + 1]) {
    const forensicRequestId = pathSegments[forensicRequestIndex + 1];
    
    return {
      type: QR_CODE_TYPES.FORENSIC_REQUEST,
      isValid: true,
      id: forensicRequestId,
      data: {
        forensicRequestId,
        url: url.href
      }
    };
  }
  
  return {
    type: QR_CODE_TYPES.FORENSIC_REQUEST,
    isValid: false,
    error: 'Invalid forensic request URL format'
  };
};

/**
 * Parse evidence packaging URLs
 * Pattern: /evidencePackaging/{evidencePackagingId}
 */
const parseEvidencePackagingUrl = (pathSegments, url) => {
  const evidencePackagingIndex = pathSegments.indexOf('evidencePackaging');
  
  if (evidencePackagingIndex !== -1 && pathSegments[evidencePackagingIndex + 1]) {
    const evidencePackagingId = pathSegments[evidencePackagingIndex + 1];
    
    return {
      type: QR_CODE_TYPES.EVIDENCE_PACKAGING,
      isValid: true,
      id: evidencePackagingId,
      data: {
        evidencePackagingId,
        url: url.href
      }
    };
  }
  
  return {
    type: QR_CODE_TYPES.EVIDENCE_PACKAGING,
    isValid: false,
    error: 'Invalid evidence packaging URL format'
  };
};

/**
 * Parse case URLs
 * Pattern: /cases/{caseId} or /cases/{caseId}/evidences/{evidenceId}
 */
const parseCaseUrl = (pathSegments, url) => {
  const caseIndex = pathSegments.indexOf('cases');
  
  if (caseIndex !== -1 && pathSegments[caseIndex + 1]) {
    const caseId = pathSegments[caseIndex + 1];
    
    // Check if it's a case evidence URL
    const evidenceIndex = pathSegments.indexOf('evidences');
    if (evidenceIndex !== -1 && pathSegments[evidenceIndex + 1]) {
      const evidenceId = pathSegments[evidenceIndex + 1];
      
      return {
        type: QR_CODE_TYPES.EVIDENCE,
        isValid: true,
        id: evidenceId,
        data: {
          caseId,
          evidenceId,
          url: url.href
        }
      };
    }
    
    return {
      type: QR_CODE_TYPES.CASE,
      isValid: true,
      id: caseId,
      data: {
        caseId,
        url: url.href
      }
    };
  }
  
  return {
    type: QR_CODE_TYPES.CASE,
    isValid: false,
    error: 'Invalid case URL format'
  };
};

/**
 * Parse evidence URLs
 * Pattern: /evidences/{evidenceId}
 */
const parseEvidenceUrl = (pathSegments, url) => {
  const evidenceIndex = pathSegments.indexOf('evidences');
  
  if (evidenceIndex !== -1 && pathSegments[evidenceIndex + 1]) {
    const evidenceId = pathSegments[evidenceIndex + 1];
    
    return {
      type: QR_CODE_TYPES.EVIDENCE,
      isValid: true,
      id: evidenceId,
      data: {
        evidenceId,
        url: url.href
      }
    };
  }
  
  return {
    type: QR_CODE_TYPES.EVIDENCE,
    isValid: false,
    error: 'Invalid evidence URL format'
  };
};

/**
 * Get navigation parameters based on QR code type
 * @param {object} parsedQR - Parsed QR code data
 * @returns {object} - Navigation parameters
 */
export const getNavigationParams = (parsedQR) => {
  if (!parsedQR.isValid) {
    return null;
  }
  
  switch (parsedQR.type) {
    case QR_CODE_TYPES.FORENSIC_REQUEST:
      return {
        pathname: '(forensic)/caseDetails',
        params: { forensicRequestId: parsedQR.data.forensicRequestId }
      };
      
    case QR_CODE_TYPES.EVIDENCE_PACKAGING:
      return {
        pathname: '(forensic)/(screens)/evidencePackageDetails',
        params: { evidencePackagingId: parsedQR.data.evidencePackagingId }
      };
      
    case QR_CODE_TYPES.CASE:
      return {
        pathname: '(forensic)/(screens)/caseOverview',
        params: { caseId: parsedQR.data.caseId }
      };
      
    case QR_CODE_TYPES.EVIDENCE:
      return {
        pathname: '(forensic)/(screens)/evidenceDetails',
        params: { 
          evidenceId: parsedQR.data.evidenceId,
          caseId: parsedQR.data.caseId 
        }
      };
      
    default:
      return null;
  }
};

/**
 * Get user-friendly error messages
 * @param {object} parsedQR - Parsed QR code data
 * @returns {string} - Error message
 */
export const getErrorMessage = (parsedQR) => {
  if (parsedQR.isValid) {
    return null;
  }
  
  switch (parsedQR.type) {
    case QR_CODE_TYPES.FORENSIC_REQUEST:
      return 'Invalid forensic request QR code. Please scan a valid forensic request QR code.';
      
    case QR_CODE_TYPES.EVIDENCE_PACKAGING:
      return 'Invalid evidence packaging QR code. Please scan a valid evidence packaging QR code.';
      
    case QR_CODE_TYPES.CASE:
      return 'Invalid case QR code. Please scan a valid case QR code.';
      
    case QR_CODE_TYPES.EVIDENCE:
      return 'Invalid evidence QR code. Please scan a valid evidence QR code.';
      
    default:
      return 'Unrecognized QR code format. Please scan a valid QR code.';
  }
};
