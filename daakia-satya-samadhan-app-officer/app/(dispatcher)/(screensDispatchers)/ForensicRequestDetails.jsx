import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Image,
  Alert,
  StyleSheet,
  ActivityIndicator,
  FlatList,
  Modal,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { transformUrl } from '../../../utils/transformUrl';
import { useAuth } from '../../../context/auth-context';
import SuccessScreen from '../../../components/Smalls/SuccessScreen';
import { MaterialIcons } from '@expo/vector-icons';
import PreviewComponent from '../../../components/Larges/PreviewComponent';
import { Colors } from '../../../constants/colors';
import { apiService } from '../../../services/api';



const ForensicRequestDetails = () => {
  const params = useLocalSearchParams();


  let forensicRequestId;
  let boxId;
  let caseIds;



  if (params.forensicRequestId) {
    forensicRequestId = params.forensicRequestId;
  } else if (params.caseData) {
    try {
      const caseData = JSON.parse(params.caseData);
      forensicRequestId = caseData.forensicRequestId;
    } catch (e) {
      console.error('Error parsing case data:', e);
    }
  }

  // Extract boxId and caseIds from packageData if available
  if (params.packageData) {
    try {
      const packageData = JSON.parse(params.packageData);
      boxId = packageData.boxId;
      caseIds = packageData.caseIds; // Array of case IDs
    } catch (e) {
      console.error('Error parsing package data:', e);
    }
  }
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);
  const [segregatedEvidences, setSegregatedEvidences] = useState(null);
  const [selectedEvidences, setSelectedEvidences] = useState({});
  const [allEvidencesSelected, setAllEvidencesSelected] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { token } = useAuth();
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [isVideo, setIsVideo] = useState(false);
  const [extractedForensicRequestIds, setExtractedForensicRequestIds] = useState([]);
  const [allForensicData, setAllForensicData] = useState([]);

  // Note: fetchForensicRequestEvidences API call removed as getEvidencesOfABox provides all needed data

  // Fetch evidence box data when boxId is available
  useEffect(() => {
    const fetchEvidenceBoxData = async () => {
      if (!boxId || !token) {
        return;
      }

      try {
        const result = await apiService.getEvidencesOfABox(token, boxId);

        // Log response without QR codes
        const cleanResult = JSON.parse(JSON.stringify(result));
        if (cleanResult.data?.evidencePackaging) {
          if (cleanResult.data.evidencePackaging.qrCode) {
            delete cleanResult.data.evidencePackaging.qrCode;
          }
          if (cleanResult.data.evidencePackaging.evidenceId) {
            cleanResult.data.evidencePackaging.evidenceId.forEach(evidence => {
              if (evidence.forensicRequests) {
                evidence.forensicRequests.forEach(request => {
                  if (request.qrCode) delete request.qrCode;
                });
              }
              if (evidence.qrCode) delete evidence.qrCode;
            });
          }
        }
        console.log('Evidence Box API Response (without QR codes):', JSON.stringify(cleanResult, null, 2));

        // Extract all forensicRequestIds from the response and call fetchForensicRequestEvidences for each
        if (result && result.data && result.data.evidencePackaging) {
          const evidencePackaging = result.data.evidencePackaging;
          const forensicRequestIds = [];
          const forensicDataArray = [];
          const evidenceLabAssignments = {}; // Store lab assignments with priorities

          // Collect all forensic request IDs and lab assignments
          if (evidencePackaging.evidenceId && Array.isArray(evidencePackaging.evidenceId)) {
            for (const evidence of evidencePackaging.evidenceId) {
              // Store lab assignments with priorities for this evidence
              if (evidence.labAssignments && Array.isArray(evidence.labAssignments)) {
                evidenceLabAssignments[evidence._id] = evidence.labAssignments;
              }

              if (evidence.forensicRequests && Array.isArray(evidence.forensicRequests)) {
                for (const forensicRequest of evidence.forensicRequests) {
                  if (forensicRequest._id) {
                    forensicRequestIds.push({
                      id: forensicRequest._id,
                      evidenceId: evidence._id,
                      labAssignments: evidence.labAssignments || []
                    });
                  }
                }
              }
            }
          }



          setExtractedForensicRequestIds(forensicRequestIds.map(item => item.id));

          // Use data directly from getEvidencesOfABox - no need for additional API calls
          const evidenceData = evidencePackaging.evidenceId;
          // Clear the existing array and reuse it
          forensicDataArray.length = 0;

          for (const evidence of evidenceData) {
            if (evidence.forensicRequests && Array.isArray(evidence.forensicRequests)) {
              for (const forensicRequest of evidence.forensicRequests) {
                // Create unified data structure from evidence box data
                const unifiedData = {
                  _id: forensicRequest._id,
                  evidence: {
                    _id: evidence._id,
                    title: evidence.title,
                    type: evidence.type || 'Unknown',
                    description: evidence.description || '',
                    attachmentUrl: forensicRequest.evidence?.attachmentUrl || []
                  },
                  labId: evidence.labAssignments?.[0]?.labId,
                  labdepartmentId: evidence.labAssignments?.[0]?.labDepartmentId,
                  labAssignments: evidence.labAssignments || []
                };
                
                forensicDataArray.push(unifiedData);
              }
            }
          }

          // Store all forensic data
          setAllForensicData(forensicDataArray);

          // Create unified evidence list (each evidence appears only once)
          if (forensicDataArray.length > 0) {
            const unifiedEvidences = createUnifiedEvidenceList(forensicDataArray);



            // Store unified evidence list instead of segregated
            setData({ unifiedEvidences });
            setSegregatedEvidences(null); // Not using segregated approach anymore
            setLoading(false);
          }
        }
      } catch (error) {
        console.error('Error fetching evidence box data:', error);
        setError(error.message || 'Failed to fetch evidence box data');
        setLoading(false);
      }
    };

    fetchEvidenceBoxData();
  }, [boxId, token]);

  // Function to segregate evidences by lab and department
  const segregateEvidences = (data) => {
    const segregated = {};

    // Check if data has the expected structure
    if (!data.evidence) {
      return segregated;
    }

    // Create a single evidence object
    const evidence = data.evidence;
    const labId = data.labId?._id || 'unknown';
    const labName = data.labId?.name || 'Unknown Lab';
    const departmentId = data.labdepartmentId?._id || 'unknown';
    const departmentName = data.labdepartmentId?.name || 'Unknown Department';

    // Initialize lab if it doesn't exist
    if (!segregated[labId]) {
      segregated[labId] = {
        name: labName,
        departments: {}
      };
    }

    // Initialize department if it doesn't exist
    if (!segregated[labId].departments[departmentId]) {
      segregated[labId].departments[departmentId] = {
        name: departmentName,
        evidences: []
      };
    }

    // Add evidence to the department
    segregated[labId].departments[departmentId].evidences.push({
      ...evidence,
      forensicRequestId: data._id
    });

    return segregated;
  };

  // Function to create a unified evidence list (each evidence appears only once)
  const createUnifiedEvidenceList = (forensicDataArray) => {
    const evidenceMap = new Map();

    for (const data of forensicDataArray) {
      if (!data.evidence) continue;

      const evidence = data.evidence;
      const evidenceId = evidence._id;

      if (!evidenceMap.has(evidenceId)) {
        // First time seeing this evidence
        evidenceMap.set(evidenceId, {
          ...evidence,
          forensicRequestId: data._id,
          allLabAssignments: data.labAssignments || [],
          departments: []
        });
      }

      // Add department assignments for this evidence
      if (data.labAssignments && Array.isArray(data.labAssignments)) {
        const currentEvidence = evidenceMap.get(evidenceId);
        data.labAssignments.forEach(assignment => {
          const deptInfo = {
            labId: assignment.labId?._id,
            labName: assignment.labId?.name,
            departmentId: assignment.labDepartmentId?._id,
            departmentName: assignment.labDepartmentId?.name,
            priority: assignment.priority
          };

          // Avoid duplicates
          const exists = currentEvidence.departments.find(d =>
            d.departmentId === deptInfo.departmentId
          );
          if (!exists) {
            currentEvidence.departments.push(deptInfo);
          }
        });
      }
    }

    // Convert map to array and sort by priority
    const evidenceList = Array.from(evidenceMap.values());

    // Sort departments within each evidence by priority
    evidenceList.forEach(evidence => {
      evidence.departments.sort((a, b) => a.priority - b.priority);
    });

    return evidenceList;
  };

  const toggleUnifiedEvidenceSelection = (evidence) => {
    setSelectedEvidences((prev) => {
      const newSelection = { ...prev };

      if (newSelection[evidence._id]) {
        // Deselect evidence
        delete newSelection[evidence._id];
      } else {
        // Select evidence
        newSelection[evidence._id] = {
          evidenceId: evidence._id,
          forensicRequestId: evidence.forensicRequestId,
          departments: evidence.departments,
        };
      }

      // Update allEvidencesSelected state
      const totalEvidences = data?.unifiedEvidences?.length || 0;
      setAllEvidencesSelected(Object.keys(newSelection).length === totalEvidences);

      return newSelection;
    });
  };

  const toggleAllEvidences = () => {
    if (allEvidencesSelected) {
      // Deselect all evidences
      setSelectedEvidences({});
      setAllEvidencesSelected(false);
    } else {
      // Select all evidences
      const allEvidences = {};

      if (data?.unifiedEvidences) {
        data.unifiedEvidences.forEach(evidence => {
          allEvidences[evidence._id] = {
            evidenceId: evidence._id,
            forensicRequestId: evidence.forensicRequestId,
            departments: evidence.departments,
          };
        });
      }

      setSelectedEvidences(allEvidences);
      setAllEvidencesSelected(true);
    }
  };

  const toggleEvidenceSelection = (labId, departmentId, evidence) => {
    setSelectedEvidences((prev) => {
      const key = `${labId}-${departmentId}-${evidence._id}`;
      let newSelection = { ...prev };

      if (newSelection[key]) {
        // Deselecting this evidence in this department
        delete newSelection[key];
      } else {
        // Selecting this evidence in this department
        newSelection[key] = {
          labId,
          departmentId,
          evidenceId: evidence._id,
          forensicRequestId: evidence.forensicRequestId,
          priority: evidence.priority,
        };
      }

      // Update allEvidencesSelected state based on selection
      const totalEvidences = countTotalEvidences();
      setAllEvidencesSelected(Object.keys(newSelection).length === totalEvidences);

      return newSelection;
    });
  };

  const countTotalEvidences = () => {
    return data?.unifiedEvidences?.length || 0;
  };

  const isEvidenceSelected = (labId, departmentId, evidenceId) => {
    const key = `${labId}-${departmentId}-${evidenceId}`;
    return !!selectedEvidences[key];
  };
  
  const isMediaVideo = (url) => {
    if (!url) return false;
    return url.toLowerCase().endsWith('.mp4') || 
           url.toLowerCase().endsWith('.mov') || 
           url.toLowerCase().endsWith('.avi');
  };

  const openMediaModal = (url) => {
    const isVideoFile = isMediaVideo(url);
    setIsVideo(isVideoFile);
    setSelectedMedia(transformUrl(url));
    setModalVisible(true);
  };

  const handleDispatch = async () => {
    if (Object.keys(selectedEvidences).length === 0) {
      Alert.alert('Error', 'Please select at least one evidence to dispatch');
      return;
    }

    setIsSubmitting(true);

    try {
      if (!token) {
        throw new Error('Authentication token not found');
      }

      if (!boxId) {
        Alert.alert('Error', 'Evidence box ID is missing');
        return;
      }

      // Step 1: Create new evidence box packaging with type 'forensic'
      console.log('Step 1: Creating new evidence box packaging...');

      // Get selected evidence IDs from user selection
      const selectedEvidenceIds = Object.keys(selectedEvidences);

      // Get packaging URLs from the original box data
      const evidencePackaging = data?.data?.evidencePackaging;
      const packagingUrls = evidencePackaging?.packagingUrl || [];

      console.log('Selected Evidence IDs:', selectedEvidenceIds);
      console.log('Packaging URLs:', packagingUrls);
      console.log('Data being sent:', {
        packagingUrl: packagingUrls,
        evidenceId: selectedEvidenceIds,
        type: 'forensic'
      });

      const newBoxResult = await apiService.addEvidenceBoxPackaging(token, {
        packagingUrl: packagingUrls,
        evidenceId: selectedEvidenceIds,
        type: 'forensic'
      });

      if (newBoxResult.status !== 'success') {
        throw new Error(newBoxResult.message || 'Failed to create new evidence box packaging');
      }
      console.log('Step 1: Success - New evidence box created');

      // Step 2: Update original evidence box packaging status to 'accepted'
      console.log('Step 2: Updating original evidence box status...');
      const updateResult = await apiService.updateEvidenceBoxPackaging(token, boxId, {
        status: 'accepted',
        remarks: 'Evidence accepted for dispatch'
      });

      if (updateResult.status !== 'success') {
        throw new Error(updateResult.message || 'Failed to update evidence box packaging status');
      }
      console.log('Step 2: Success - Original box status updated');

      // Step 3: Dispatch each forensic request in parallel
      console.log('Step 3: Dispatching forensic requests...');
      console.log('Forensic request IDs:', extractedForensicRequestIds);

      const dispatchPromises = extractedForensicRequestIds.map(forensicRequestId =>
        apiService.dispatchForensicRequest(token, forensicRequestId)
      );

      const dispatchResults = await Promise.all(dispatchPromises);
      console.log('Step 3: Success - All forensic requests dispatched');


      // Check if all dispatches were successful
      const failedDispatches = dispatchResults.filter(result => result.status !== 'success');
      if (failedDispatches.length > 0) {
        throw new Error(`Failed to dispatch ${failedDispatches.length} forensic requests`);
      }

      // Extract QR code from the new evidence box (Step 1 result)
      const dispatcherQr = newBoxResult.data?.qrCode;
      const requestId = newBoxResult.data?._id;
      const caseId = newBoxResult.data?.caseId;
      
      // Reset state
      setSelectedEvidences({});
      setAllEvidencesSelected(false);
  
      setShowSuccessScreen(true);
      setTimeout(() => {
        setShowSuccessScreen(false);
        router.replace({
          pathname: '(screensDispatchers)/dipatchQr',
          params: { 
            dispatchData: JSON.stringify({
              dispatcherQr,
              requestId,
              caseId
            }) 
          }
        });
      }, 3000);
      
    } catch (error) {
      console.error('Error dispatching evidence:', error);
      Alert.alert('Error', `Failed to dispatch evidence: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderUnifiedEvidenceCard = (evidence) => {
    const isSelected = selectedEvidences[evidence._id] || false;
    const hasAttachments = evidence.attachmentUrl && evidence.attachmentUrl.length > 0;

    return (
      <TouchableOpacity
        style={[
          styles.evidenceCard,
          isSelected && styles.selectedEvidenceCard
        ]}
        onPress={() => toggleUnifiedEvidenceSelection(evidence)}
      >
        <View style={styles.evidenceHeader}>
          <View style={styles.evidenceTitleRow}>
            <Text style={styles.evidenceTitle} numberOfLines={1}>
              {evidence.title || `Evidence ID: ${evidence._id.substring(evidence._id.length - 6)}`}
            </Text>
          </View>
          <View style={styles.evidenceInfoRow}>
            <Text style={styles.evidenceType}>{evidence.type || "Unknown Type"}</Text>
            <Text style={styles.evidenceCount}>
              {evidence.departments.length} dept{evidence.departments.length !== 1 ? 's' : ''}
            </Text>
          </View>

          {/* Show all department assignments in a compact grid */}
          <View style={styles.departmentsContainer}>
            <View style={styles.departmentsGrid}>
              {evidence.departments.map((dept) => (
                <View key={dept.departmentId} style={styles.departmentChip}>
                  <Text style={styles.departmentChipText}>
                    {dept.departmentName === 'DNA Analysis' ? 'DNA' :
                     dept.departmentName === 'Toxicology' ? 'Tox' :
                     dept.departmentName}
                  </Text>
                  <View style={styles.priorityChip}>
                    <Text style={styles.priorityChipText}>{dept.priority}</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        </View>

        {hasAttachments ? (
          <FlatList
            horizontal
            data={evidence.attachmentUrl}
            keyExtractor={(item, index) => `attachment-${index}`}
            showsHorizontalScrollIndicator={false}
            renderItem={({ item, index }) => (
              <TouchableOpacity
                style={styles.mediaItem}
                onPress={() => openMediaModal(item)}
              >
                {isMediaVideo(item) ? (
                  <View style={styles.videoContainer}>
                    <Image
                      source={{ uri: transformUrl(item.replace(/\.(mp4|mov|avi)$/, '.jpg')) }}
                      style={styles.attachmentImage}
                      resizeMode="cover"
                    />
                    <View style={styles.videoOverlay}>
                      <MaterialIcons name="play-circle-fill" size={40} color="white" />
                      <Text style={styles.videoLabel}>VIDEO</Text>
                    </View>
                  </View>
                ) : (
                  <Image
                    source={{ uri: transformUrl(item) }}
                    style={styles.attachmentImage}
                    resizeMode="cover"
                  />
                )}
                <View style={styles.attachmentNumberContainer}>
                  <Text style={styles.attachmentNumber}>{index + 1}</Text>
                </View>
              </TouchableOpacity>
            )}
          />
        ) : (
          <View style={styles.noAttachmentsContainer}>
            <MaterialIcons name="image-not-supported" size={40} color="#ccc" />
            <Text style={styles.noAttachmentsText}>No Media Available</Text>
          </View>
        )}

        {evidence.description && (
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionLabel}>Description: <Text style={styles.descriptionText} numberOfLines={2}>{evidence.description} </Text></Text>
          </View>
        )}

        {isSelected && (
          <View style={styles.checkmarkOverlay}>
            <MaterialIcons name="check-circle" size={24} color={Colors.primary} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderEvidenceCard = (evidence, labId, departmentId) => {
    const isSelected = isEvidenceSelected(labId, departmentId, evidence._id);
    const hasAttachments = evidence.attachmentUrl && evidence.attachmentUrl.length > 0;
    const departmentName = segregatedEvidences[labId]?.departments[departmentId]?.name || 'Unknown Department';

    return (
      <TouchableOpacity
        style={[
          styles.evidenceCard,
          isSelected && styles.selectedEvidenceCard
        ]}
        onPress={() => toggleEvidenceSelection(labId, departmentId, evidence)}
      >
        <View style={styles.evidenceHeader}>
          <View style={styles.evidenceTitleRow}>
            <Text style={styles.evidenceTitle} numberOfLines={1}>
              {evidence.title || `Evidence ID: ${evidence._id.substring(evidence._id.length - 6)}`}
            </Text>
            {/* Show small badge if evidence appears in multiple departments */}
            {evidence.allLabAssignments && evidence.allLabAssignments.length > 1 && (
              <View style={styles.multiDeptBadge}>
                <Text style={styles.multiDeptBadgeText}>{evidence.allLabAssignments.length}D</Text>
              </View>
            )}
          </View>
          <View style={styles.evidenceTypeContainer}>
            <Text style={styles.evidenceType}>{evidence.type || "Unknown Type"}</Text>
            <View style={styles.departmentContainer}>
              <View style={styles.departmentRow}>
                <Text style={styles.departmentLabel}>Current Dept: </Text>
                <Text style={styles.departmentName}>{departmentName}</Text>
              </View>
              {evidence.priority && (
                <View style={styles.currentPriorityContainer}>
                  <Text style={styles.priorityLabel}>Priority: </Text>
                  <Text style={styles.priorityValue}>{evidence.priority}</Text>
                </View>
              )}
            </View>
          </View>
        </View>
        
        {hasAttachments ? (
          <FlatList
            horizontal
            data={evidence.attachmentUrl}
            keyExtractor={(item, index) => `attachment-${index}`}
            showsHorizontalScrollIndicator={false}
            renderItem={({ item, index }) => (
              <TouchableOpacity
                style={styles.mediaItem}
                onPress={() => openMediaModal(item)}
              >
                {isMediaVideo(item) ? (
                  <View style={styles.videoContainer}>
                    <Image
                      source={{ uri: transformUrl(item.replace(/\.(mp4|mov|avi)$/, '.jpg')) }}
                      style={styles.attachmentImage}
                      resizeMode="cover"
                    />
                    <View style={styles.videoOverlay}>
                      <MaterialIcons name="play-circle-fill" size={40} color="white" />
                      <Text style={styles.videoLabel}>VIDEO</Text>
                    </View>
                  </View>
                ) : (
                  <Image
                    source={{ uri: transformUrl(item) }}
                    style={styles.attachmentImage}
                    resizeMode="cover"
                  />
                )}
                <View style={styles.attachmentNumberContainer}>
                  <Text style={styles.attachmentNumber}>{index + 1}</Text>
                </View>
              </TouchableOpacity>
            )}
          />
        ) : (
          <View style={styles.noAttachmentsContainer}>
            <MaterialIcons name="image-not-supported" size={40} color="#ccc" />
            <Text style={styles.noAttachmentsText}>No Media Available</Text>
          </View>
        )}
        
        {evidence.description && (
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionLabel}>Description: <Text style={styles.descriptionText} numberOfLines={2}>{evidence.description} </Text></Text>
     
          </View>
        )}

        {/* Only show lab name */}
        {/* {evidence.labId && (
          <Text style={styles.evidenceLabel}>
            Lab: <Text style={styles.evidenceType}>
              {typeof evidence.labId === 'object' ? evidence.labId.name : 'Unknown Lab'}
            </Text>
          </Text>
        )} */}
        
        {isSelected && (
          <View style={styles.checkmarkOverlay}>
            <MaterialIcons name="check-circle" size={24} color={Colors.primary} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderDepartmentSection = (departmentId, department, labId) => {
    const departmentName = department.name || 'Unknown Department';
    const evidences = department.evidences || [];
    const departmentPriority = department.priority;

    return (
      <View key={departmentId} style={styles.departmentSection}>
        <View style={styles.departmentHeader}>
          <View style={styles.departmentTitleContainer}>
            <Text style={styles.departmentName}>{departmentName}</Text>
            {departmentPriority && (
              <View style={styles.departmentPriorityContainer}>
                <Text style={styles.departmentPriorityLabel}>Priority: </Text>
                <Text style={styles.departmentPriorityValue}>{departmentPriority}</Text>
              </View>
            )}
          </View>
          <Text style={styles.evidenceCount}>({evidences.length} {evidences.length === 1 ? 'evidence' : 'evidences'})</Text>
        </View>

        <View style={styles.evidenceGrid}>
          {evidences.map(evidence => (
            <View key={evidence._id} style={styles.evidenceContainer}>
              {renderEvidenceCard(evidence, labId, departmentId)}
            </View>
          ))}
        </View>
      </View>
    );
  };

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <StatusBar style="dark" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0a34a1" />
          <Text style={styles.loadingText}>Loading evidence data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <StatusBar style="dark" />
        <View style={styles.container}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // No evidence data
  if (!data?.unifiedEvidences || data.unifiedEvidences.length === 0) {
    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <StatusBar style="dark" />
        <View style={styles.container}>
          <Text style={styles.errorText}>No evidence data available.</Text>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeAreaContainer}>
      {/* Preview Component should be one of the first elements */}
      {modalVisible && (
        <View style={styles.previewOverlay}>
          <PreviewComponent
            uri={selectedMedia}
            onClose={() => setModalVisible(false)}
          />
        </View>
      )}

      <StatusBar style="dark" />
      
      <View style={styles.headerContainer}>
        <View style={styles.titleContainer}>
          <Text style={styles.headerTitle}>Evidence for Dispatch</Text>
          <Text style={styles.subTitle}>
            {data?.caseId?.title || 'Case Details'}
          </Text>
        </View>
        
        <View style={styles.selectionControls}>
          <TouchableOpacity
            style={styles.selectAllButton}
            onPress={toggleAllEvidences}
          >
            <MaterialIcons
              name={allEvidencesSelected ? "check-box" : "check-box-outline-blank"}
              size={22}
              color="#0a34a1"
            />
            <Text style={styles.selectAllText}>
              {allEvidencesSelected ? "Deselect All" : "Select All"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <ScrollView style={styles.container}>
        <View style={styles.labContainer}>
          <View style={styles.labHeader}>
            <Text style={styles.labName}>Central Forensic Science Laboratory</Text>
            <View style={styles.labCountContainer}>
              <Text style={styles.labCountText}>
                {data.unifiedEvidences.length} {data.unifiedEvidences.length === 1 ? 'Evidence' : 'Evidences'}
              </Text>
            </View>
          </View>

          <View style={styles.evidenceGrid}>
            {data.unifiedEvidences.map(evidence => (
              <View key={evidence._id} style={styles.evidenceContainer}>
                {renderUnifiedEvidenceCard(evidence)}
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
      
      {/* Dispatch button at the bottom */}
      <View style={styles.bottomBar}>
        <View style={styles.selectionSummary}>
          <Text style={styles.selectionCount}>
            {Object.keys(selectedEvidences).length} of {countTotalEvidences()} selected
          </Text>
        </View>
        <TouchableOpacity
          style={[
            styles.dispatchButton,
            Object.keys(selectedEvidences).length === 0 && styles.disabledButton,
            isSubmitting && styles.submittingButton,
          ]}
          onPress={handleDispatch}
          disabled={Object.keys(selectedEvidences).length === 0 || isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <>
              <MaterialIcons name="send" size={18} color="#fff" style={styles.dispatchIcon} />
              <Text style={styles.dispatchButtonText}>Dispatch Selected</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
      
      {showSuccessScreen && (
        <SuccessScreen 
          message="Evidence dispatched successfully to the labs!" 
          duration={3000}
          onComplete={() => {}}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerContainer: {
    backgroundColor: '#fff',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    fontFamily: 'Roboto_bold',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0a34a1',
    fontFamily: 'Roboto_bold',
  },
  subTitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  selectionControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  selectAllText: {
    marginLeft: 4,
    color: '#0a34a1',
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
    fontFamily: 'Roboto',
  },
  labContainer: {
    marginHorizontal: 12,
    marginVertical: 8,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  labHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  labName: {
    fontSize: 17,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    fontFamily: 'Roboto_bold',
  },
  labCountContainer: {
    backgroundColor: '#f0f4ff',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  labCountText: {
    fontSize: 13,
    color: '#0a34a1',
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  departmentSection: {
    marginBottom: 16,
    fontFamily: 'Roboto',
  },
  departmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    fontFamily: 'Roboto_bold',
  },
  departmentTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  departmentName: {
    fontSize: 15,
    fontWeight: '600',
    color: '#444',
    marginRight: 8,
    fontFamily: 'Roboto',
  },
  departmentPriorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e8f4fd',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
  },
  departmentPriorityLabel: {
    fontSize: 12,
    color: '#0a34a1',
    fontWeight: '500',
  },
  departmentPriorityValue: {
    fontSize: 12,
    color: '#0a34a1',
    fontWeight: 'bold',
  },
  evidenceCount: {
    fontSize: 13,
    color: '#666',
    fontFamily: 'Roboto',
  },
  evidenceGrid: {
    marginLeft: 4,
  },
  evidenceContainer: {
    marginBottom: 12,
  },
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginBottom: 12,
    padding: 12,
    borderRadius: 10,
  },
  selectedEvidenceCard: {
    borderColor: Colors.primary,
    borderWidth: 2,
    backgroundColor: `${Colors.primary}10`,
  },
  evidenceHeader: {
    marginBottom: 10,
  },
  evidenceTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  evidenceTitle: {
    fontSize: 15,
    color: Colors.black,
    fontWeight: '500',
    flex: 1,
    marginRight: 8,
  },
  evidenceTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  evidenceInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 2,
    marginBottom: 2,
  },
  evidenceType: {
    fontSize: 12,
    color: Colors.lightText,
    fontWeight: '500',
  },
  evidenceCount: {
    fontSize: 10,
    color: '#0a34a1',
    fontWeight: '600',
    backgroundColor: '#f0f4ff',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  departmentContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  departmentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  departmentLabel: {
    fontSize: 13,
    color: Colors.lightText,
    fontWeight: '500',
  },
  currentPriorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e8f4fd',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginTop: 2,
  },
  priorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityLabel: {
    fontSize: 13,
    color: Colors.lightText,
    fontWeight: '500',
  },
  priorityValue: {
    fontSize: 13,
    color: '#0a34a1',
    fontWeight: 'bold',
    backgroundColor: '#f0f4ff',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  multiDeptBadge: {
    backgroundColor: '#ffc107',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  multiDeptBadgeText: {
    fontSize: 10,
    color: '#000',
    fontWeight: 'bold',
  },
  departmentsContainer: {
    marginTop: 2,
  },
  departmentsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  departmentChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f4ff',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 3,
    marginRight: 4,
    marginBottom: 3,
    borderWidth: 1,
    borderColor: '#e0e7ff',
  },
  departmentChipText: {
    fontSize: 10,
    color: '#374151',
    fontWeight: '500',
    marginRight: 4,
  },
  priorityChip: {
    backgroundColor: '#0a34a1',
    borderRadius: 8,
    paddingHorizontal: 4,
    paddingVertical: 1,
    minWidth: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  priorityChipText: {
    fontSize: 9,
    color: 'white',
    fontWeight: 'bold',
  },
  mediaItem: {
    width: 120,
    height: 120,
    marginRight: 10,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  attachmentImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  attachmentNumberContainer: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  attachmentNumber: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  videoContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: Colors.background,
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    marginTop: 8,
  },
  noAttachmentsContainer: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 10,
  },
  noAttachmentsText: {
    color: '#999',
    fontSize: 14,
    marginTop: 8,
  },
  descriptionContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    fontFamily: 'Roboto',
  },
  descriptionLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: '#666',
    marginBottom: 4,
    fontFamily: 'Roboto',
  },
  descriptionText: {
    fontSize: 13,
    color: '#555',
    fontFamily: 'Roboto',
  },
  checkmarkOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  bottomBar: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  selectionSummary: {
    flex: 1,
  },
  selectionCount: {
    fontSize: 14,
    color: '#666',
  },
  dispatchButton: {
    backgroundColor: '#0a34a1',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 24,
    flexDirection: 'row',
    alignItems: 'center',
    fontFamily: 'Roboto_bold',
  },
  disabledButton: {
    backgroundColor: '#9eb0d5',
  },
  submittingButton: {
    backgroundColor: '#0a34a1',
    fontFamily: 'Roboto_bold',
    opacity: 0.7,
  },
  dispatchIcon: {
    marginRight: 8,
  },
  dispatchButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
    textAlign: 'center',
    marginTop: 20,
    padding: 16,
  },
  backButton: {
    backgroundColor: '#0a34a1',
    padding: 10,
    borderRadius: 8,
    width: 100,
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 20,

  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  evidenceLabel: {
    fontSize: 13,
    color: Colors.lightText,
    marginTop: 8,
  },
  previewOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000, // Ensure it's above other content
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
});

export default ForensicRequestDetails;